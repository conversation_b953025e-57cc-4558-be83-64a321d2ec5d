<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | Xerv</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Inter:wght@400;700&family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xerv-dark': '#0F1118',
                        'xerv-blue': '#00B7FF',
                        'xerv-magenta': '#FF007F',
                        'xerv-gray': '#262626',
                        'xerv-light': '#F1F1F1',
                        'admin-green': '#10B981',
                        'admin-orange': '#F59E0B'
                    },
                    fontFamily: {
                        'heading': ['Bebas Neue', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        .glass-card {
            background: rgba(38, 38, 38, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .admin-access {
            display: none;
        }
        .pulse-glow {
            animation: pulseGlow 2s infinite;
        }
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 5px rgba(16, 185, 129, 0.5); }
            50% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.8); }
        }
    </style>
</head>
<body class="bg-xerv-dark text-white min-h-screen">
    <!-- 404 Page (Default View) -->
    <div id="error-page" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-xerv-dark via-gray-900 to-black">
        <div class="text-center px-4 sm:px-6 lg:px-8 max-w-2xl mx-auto">
            <div class="mb-8">
                <h1 class="font-heading text-8xl md:text-9xl text-xerv-blue mb-4">404</h1>
                <h2 class="font-heading text-3xl md:text-4xl text-white mb-6">Page Not Found</h2>
                <p class="text-xl text-gray-300 mb-8">
                    The page you're looking for doesn't exist or has been moved.
                </p>
            </div>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="index.html" class="bg-xerv-blue text-xerv-dark px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-home mr-2"></i>
                    Back to Home
                </a>
                <a href="index.html#contact" class="border-2 border-xerv-magenta text-xerv-magenta px-8 py-4 rounded-lg font-semibold text-lg hover:bg-xerv-magenta hover:text-white transition-all duration-300">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard (Hidden by Default) -->
    <div id="admin-dashboard" class="admin-access">
        <!-- Admin Navigation -->
        <nav class="bg-xerv-dark/90 backdrop-blur-md border-b border-admin-green/30 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <div class="font-heading text-2xl text-admin-green">XERV ADMIN</div>
                        <span class="ml-4 px-3 py-1 bg-admin-green/20 text-admin-green rounded-full text-sm">Dashboard</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-300 text-sm">Welcome, Admin</span>
                        <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Admin Content -->
        <div class="min-h-screen bg-gradient-to-br from-xerv-dark to-gray-900 py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Dashboard Header -->
                <div class="mb-8">
                    <h1 class="font-heading text-4xl text-white mb-2">Admin Dashboard</h1>
                    <p class="text-gray-300">Xerv Website Management Panel</p>
                </div>

                <!-- Stats Grid -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Website Visitors -->
                    <div class="glass-card p-6 rounded-xl border-admin-green/30">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Website Visitors</p>
                                <p class="text-2xl font-bold text-white">2,847</p>
                                <p class="text-admin-green text-sm">+12% this week</p>
                            </div>
                            <div class="w-12 h-12 bg-admin-green/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-admin-green"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Inquiries -->
                    <div class="glass-card p-6 rounded-xl border-admin-orange/30">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Contact Inquiries</p>
                                <p class="text-2xl font-bold text-white">47</p>
                                <p class="text-admin-orange text-sm">+8 new today</p>
                            </div>
                            <div class="w-12 h-12 bg-admin-orange/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-envelope text-admin-orange"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Project Views -->
                    <div class="glass-card p-6 rounded-xl border-xerv-blue/30">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Project Views</p>
                                <p class="text-2xl font-bold text-white">1,234</p>
                                <p class="text-xerv-blue text-sm">Portfolio section</p>
                            </div>
                            <div class="w-12 h-12 bg-xerv-blue/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-eye text-xerv-blue"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Waitlist Signups -->
                    <div class="glass-card p-6 rounded-xl border-xerv-magenta/30">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Waitlist Signups</p>
                                <p class="text-2xl font-bold text-white">156</p>
                                <p class="text-xerv-magenta text-sm">TheMeet & NeTuArk</p>
                            </div>
                            <div class="w-12 h-12 bg-xerv-magenta/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-xerv-magenta"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Recent Contact Submissions -->
                    <div class="glass-card p-6 rounded-xl">
                        <h3 class="font-heading text-xl text-white mb-4 flex items-center">
                            <i class="fas fa-inbox mr-2 text-admin-green"></i>
                            Recent Contact Submissions
                        </h3>
                        <div class="space-y-4">
                            <div class="border-l-4 border-admin-green pl-4 py-2">
                                <p class="text-white font-semibold">Service Inquiry - Web Development</p>
                                <p class="text-gray-400 text-sm"><EMAIL> • 2 hours ago</p>
                            </div>
                            <div class="border-l-4 border-admin-orange pl-4 py-2">
                                <p class="text-white font-semibold">Career Application - Web Developer</p>
                                <p class="text-gray-400 text-sm"><EMAIL> • 5 hours ago</p>
                            </div>
                            <div class="border-l-4 border-xerv-blue pl-4 py-2">
                                <p class="text-white font-semibold">Volunteer Application - Social Media</p>
                                <p class="text-gray-400 text-sm"><EMAIL> • 1 day ago</p>
                            </div>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="glass-card p-6 rounded-xl">
                        <h3 class="font-heading text-xl text-white mb-4 flex items-center">
                            <i class="fas fa-server mr-2 text-xerv-blue"></i>
                            System Status
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Website Status</span>
                                <span class="flex items-center text-admin-green">
                                    <div class="w-2 h-2 bg-admin-green rounded-full mr-2 pulse-glow"></div>
                                    Online
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Tally Forms</span>
                                <span class="flex items-center text-admin-green">
                                    <div class="w-2 h-2 bg-admin-green rounded-full mr-2 pulse-glow"></div>
                                    Active
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Email System</span>
                                <span class="flex items-center text-admin-green">
                                    <div class="w-2 h-2 bg-admin-green rounded-full mr-2 pulse-glow"></div>
                                    Operational
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Last Backup</span>
                                <span class="text-gray-400">2 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mt-8">
                    <h3 class="font-heading text-xl text-white mb-4">Quick Actions</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <button class="glass-card p-4 rounded-xl hover:border-admin-green/50 transition-all duration-300 text-left">
                            <i class="fas fa-envelope text-admin-green text-xl mb-2"></i>
                            <p class="text-white font-semibold">Check Email</p>
                            <p class="text-gray-400 text-sm">View contact submissions</p>
                        </button>
                        <button class="glass-card p-4 rounded-xl hover:border-xerv-blue/50 transition-all duration-300 text-left">
                            <i class="fas fa-chart-bar text-xerv-blue text-xl mb-2"></i>
                            <p class="text-white font-semibold">Analytics</p>
                            <p class="text-gray-400 text-sm">View detailed reports</p>
                        </button>
                        <button class="glass-card p-4 rounded-xl hover:border-xerv-magenta/50 transition-all duration-300 text-left">
                            <i class="fas fa-cog text-xerv-magenta text-xl mb-2"></i>
                            <p class="text-white font-semibold">Settings</p>
                            <p class="text-gray-400 text-sm">Manage site configuration</p>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check for admin access
        function checkAdminAccess() {
            const hash = window.location.hash;
            const urlParams = new URLSearchParams(window.location.search);
            
            // Check for specific hash or URL parameter
            if (hash === '#xerv-admin-2024' || urlParams.get('admin') === 'xerv-2024') {
                document.getElementById('error-page').style.display = 'none';
                document.getElementById('admin-dashboard').style.display = 'block';
                document.title = 'Admin Dashboard | Xerv';
                
                // Store admin session
                sessionStorage.setItem('xerv-admin', 'authenticated');
            } else if (sessionStorage.getItem('xerv-admin') === 'authenticated') {
                // Check if already authenticated in this session
                document.getElementById('error-page').style.display = 'none';
                document.getElementById('admin-dashboard').style.display = 'block';
                document.title = 'Admin Dashboard | Xerv';
            }
        }

        // Logout function
        function logout() {
            sessionStorage.removeItem('xerv-admin');
            window.location.href = 'admin.html';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', checkAdminAccess);
        
        // Check on hash change
        window.addEventListener('hashchange', checkAdminAccess);

        // Simulate real-time updates (for demo purposes)
        setInterval(() => {
            const pulseElements = document.querySelectorAll('.pulse-glow');
            pulseElements.forEach(el => {
                el.style.animation = 'none';
                setTimeout(() => {
                    el.style.animation = 'pulseGlow 2s infinite';
                }, 10);
            });
        }, 5000);
    </script>
</body>
</html>

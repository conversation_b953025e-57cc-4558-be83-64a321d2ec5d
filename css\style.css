/* css/style.css - Enhanced styles for Xerv website */
/* Custom styles to complement Tailwind CSS */

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Custom glass effect */
.glass-card {
    background: rgba(38, 38, 38, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    background: rgba(38, 38, 38, 0.5);
    border-color: rgba(0, 183, 255, 0.3);
}

/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #0F1118;
}

::-webkit-scrollbar-thumb {
    background: #00B7FF;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #FF007F;
}

/* Particle background effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(0, 183, 255, 0.05) 1px, transparent 1px),
        radial-gradient(circle at 80% 30%, rgba(255, 0, 127, 0.05) 1px, transparent 1px),
        radial-gradient(circle at 50% 70%, rgba(0, 183, 255, 0.04) 1px, transparent 1px),
        radial-gradient(circle at 30% 90%, rgba(255, 0, 127, 0.03) 1px, transparent 1px);
    background-size: 50px 50px, 70px 70px, 60px 60px, 80px 80px;
    animation: subtleParticles 20s linear infinite;
    opacity: 0.5;
}

@keyframes subtleParticles {
    0% { background-position: 0 0, 0 0, 0 0, 0 0; }
    100% { background-position: 100px 100px, -100px 100px, 100px -100px, -100px -100px; }
}

/* Enhanced hover effects */
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 183, 255, 0.4);
}

/* Custom focus styles */
.focus-visible:focus {
    outline: 2px solid #00B7FF;
    outline-offset: 2px;
}

/* Loading animation for iframe */
iframe {
    transition: opacity 0.3s ease;
}

iframe[data-tally-src]:not([src]) {
    opacity: 0.5;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .glass-card {
        backdrop-filter: blur(5px);
    }
    
    body::before {
        animation-duration: 30s;
    }
}

/* Enhanced text effects */
.text-glow {
    text-shadow: 0 0 10px rgba(0, 183, 255, 0.5);
}

/* Improved button hover effects */
.btn-hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* Custom gradient text */
.gradient-text {
    background: linear-gradient(45deg, #00B7FF, #FF007F);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Floating animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Pulse animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.pulse-animation {
    animation: pulse 2s ease-in-out infinite;
}

/* Enhanced card shadows */
.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom border animations */
.border-animate {
    position: relative;
    overflow: hidden;
}

.border-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00B7FF, transparent);
    animation: borderSlide 2s linear infinite;
}

@keyframes borderSlide {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive text sizing */
@media (max-width: 640px) {
    .responsive-text-lg {
        font-size: 1.125rem;
    }
    
    .responsive-text-xl {
        font-size: 1.25rem;
    }
    
    .responsive-text-2xl {
        font-size: 1.5rem;
    }
}

/* Print styles */
@media print {
    body::before {
        display: none;
    }
    
    .glass-card {
        background: white;
        border: 1px solid #ccc;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        background: rgba(0, 0, 0, 0.8);
        border: 2px solid white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    body::before {
        animation: none;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Xerv: Serve Humanity through innovative digital solutions. Web development, UI design, and cutting-edge technology services.">
    <meta name="keywords" content="Xerv, web development, UI design, logo design, image editing, TheMeet, NeTuArk, digital services, technology">
    <title>Xerv - Serve Humanity Through Technology</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Inter:wght@400;700&family=Lato&family=Poppins:wght@400;700&family=Roboto+Slab&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xerv-dark': '#0F1118',
                        'xerv-blue': '#00B7FF',
                        'xerv-magenta': '#FF007F',
                        'xerv-gray': '#262626',
                        'xerv-light': '#F1F1F1'
                    },
                    fontFamily: {
                        'heading': ['Bebas Neue', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        .glass-card {
            background: rgba(38, 38, 38, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .animate-fade-in {
            animation: fadeIn 2s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-xerv-dark text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 w-full z-50 bg-xerv-dark/80 backdrop-blur-md border-b border-xerv-gray/30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="font-heading text-2xl text-white">XERV</div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#hero" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                        <a href="#about" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="#services" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">Services</a>
                        <a href="#projects" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">Projects</a>
                        <a href="#works" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">Our Works</a>
                        <a href="#contact" class="text-white hover:text-xerv-blue px-3 py-2 rounded-md text-sm font-medium transition-colors">Contact</a>
                        <a href="#waitlist" class="bg-xerv-blue text-xerv-dark px-4 py-2 rounded-lg font-semibold hover:bg-white transition-colors">Join Waitlist</a>
                    </div>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-xerv-blue focus:outline-none focus:text-xerv-blue">
                        <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                            <path class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-xerv-dark/95">
                <a href="#hero" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="#about" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="#services" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">Services</a>
                <a href="#projects" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">Projects</a>
                <a href="#works" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">Our Works</a>
                <a href="#contact" class="text-white hover:text-xerv-blue block px-3 py-2 rounded-md text-base font-medium">Contact</a>
                <a href="#waitlist" class="bg-xerv-blue text-xerv-dark block px-3 py-2 rounded-md text-base font-medium">Join Waitlist</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header id="hero" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-xerv-dark via-gray-900 to-black relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-xerv-blue/5 to-xerv-magenta/5"></div>
        <div class="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
            <h1 class="font-heading text-5xl md:text-7xl lg:text-8xl text-white mb-6 animate-fade-in">
                XERV
            </h1>
            <p class="text-xl md:text-2xl text-xerv-light mb-4 font-poppins">
                Our Goal: <span class="text-xerv-blue font-bold">Serve Humanity</span>
            </p>
            <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Empowering the future through innovative digital solutions, cutting-edge technology, and human-centered design.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="#waitlist" class="bg-xerv-blue text-xerv-dark px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white transition-all duration-300 transform hover:scale-105">
                    Join the Future
                </a>
                <a href="#about" class="border-2 border-xerv-magenta text-xerv-magenta px-8 py-4 rounded-lg font-semibold text-lg hover:bg-xerv-magenta hover:text-white transition-all duration-300">
                    Learn More
                </a>
            </div>
        </div>
    </header>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gradient-to-br from-gray-900 to-xerv-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">About Xerv</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    We are a team of passionate innovators dedicated to serving humanity through technology. 
                    Our mission is to create digital solutions that make a meaningful impact on people's lives.
                </p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6 glass-card rounded-xl hover:border-xerv-blue/50 transition-all duration-300">
                    <div class="w-16 h-16 bg-xerv-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-heart text-2xl text-xerv-blue"></i>
                    </div>
                    <h3 class="font-heading text-2xl text-white mb-3">Human-Centered</h3>
                    <p class="text-gray-300">Every solution we create puts human needs and experiences at the center of our design process.</p>
                </div>
                <div class="text-center p-6 glass-card rounded-xl hover:border-xerv-blue/50 transition-all duration-300">
                    <div class="w-16 h-16 bg-xerv-magenta/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-rocket text-2xl text-xerv-magenta"></i>
                    </div>
                    <h3 class="font-heading text-2xl text-white mb-3">Innovation First</h3>
                    <p class="text-gray-300">We leverage cutting-edge technologies to build solutions that push the boundaries of what's possible.</p>
                </div>
                <div class="text-center p-6 glass-card rounded-xl hover:border-xerv-blue/50 transition-all duration-300">
                    <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-2xl text-green-500"></i>
                    </div>
                    <h3 class="font-heading text-2xl text-white mb-3">Global Impact</h3>
                    <p class="text-gray-300">Our work aims to create positive change that reaches communities around the world.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-xerv-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">Our Services</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    We provide comprehensive digital solutions to help bring your vision to life
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="group p-6 glass-card rounded-xl hover:border-xerv-blue/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-xerv-blue/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-xerv-blue/30 transition-colors">
                        <i class="fas fa-code text-2xl text-xerv-blue"></i>
                    </div>
                    <h3 class="font-heading text-xl text-white mb-3">Web Development</h3>
                    <p class="text-gray-300 text-sm">Modern, responsive websites and web applications built with cutting-edge technologies.</p>
                </div>
                <div class="group p-6 glass-card rounded-xl hover:border-xerv-magenta/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-xerv-magenta/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-xerv-magenta/30 transition-colors">
                        <i class="fas fa-palette text-2xl text-xerv-magenta"></i>
                    </div>
                    <h3 class="font-heading text-xl text-white mb-3">UI Design</h3>
                    <p class="text-gray-300 text-sm">Beautiful, intuitive user interfaces that provide exceptional user experiences.</p>
                </div>
                <div class="group p-6 glass-card rounded-xl hover:border-green-500/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-green-500/30 transition-colors">
                        <i class="fas fa-image text-2xl text-green-500"></i>
                    </div>
                    <h3 class="font-heading text-xl text-white mb-3">Image Editing</h3>
                    <p class="text-gray-300 text-sm">Professional image editing and graphic design services for all your visual needs.</p>
                </div>
                <div class="group p-6 glass-card rounded-xl hover:border-yellow-500/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-yellow-500/30 transition-colors">
                        <i class="fas fa-star text-2xl text-yellow-500"></i>
                    </div>
                    <h3 class="font-heading text-xl text-white mb-3">Logo Design</h3>
                    <p class="text-gray-300 text-sm">Memorable brand identities and logos that capture your company's essence.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Upcoming Projects Section -->
    <section id="projects" class="py-20 bg-gradient-to-br from-xerv-dark to-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">Upcoming Projects</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Revolutionary platforms that will reshape how we connect and collaborate
                </p>
            </div>
            <div class="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
                <!-- TheMeet -->
                <div class="text-center p-8 glass-card rounded-2xl hover:border-indigo-500/50 transition-all duration-300 group">
                    <div class="mb-6 flex justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="120" height="120" class="group-hover:scale-110 transition-transform duration-300">
                            <rect width="100" height="100" rx="20" fill="#4338CA" />
                            <circle cx="50" cy="40" r="15" fill="white" />
                            <path d="M25 65 L75 65 C75 85 25 85 25 65 Z" fill="white" />
                            <rect x="30" y="25" width="40" height="30" rx="5" fill="#4338CA" stroke="white" stroke-width="3" />
                            <circle cx="50" cy="40" r="8" fill="#4338CA" stroke="white" stroke-width="3" />
                        </svg>
                    </div>
                    <h3 class="font-heading text-3xl text-white mb-4">TheMeet</h3>
                    <p class="text-indigo-400 text-lg font-semibold mb-4">Meet. Share. Collaborate</p>
                    <p class="text-gray-300 mb-6">
                        The future of virtual meetings and collaboration. Connect with people worldwide in immersive,
                        interactive environments that make remote work feel natural and engaging.
                    </p>
                    <div class="flex justify-center">
                        <span class="bg-indigo-600/20 text-indigo-400 px-4 py-2 rounded-full text-sm font-medium">Coming Soon</span>
                    </div>
                </div>

                <!-- NeTuArk -->
                <div class="text-center p-8 glass-card rounded-2xl hover:border-purple-500/50 transition-all duration-300 group">
                    <div class="mb-6 flex justify-center">
                        <img src="logo2-removebg-preview.png" alt="NeTuArk Logo" class="w-30 h-30 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <h3 class="font-heading text-3xl text-white mb-4">NeTuArk</h3>
                    <p class="text-purple-400 text-lg font-semibold mb-4">The World Is Opening</p>
                    <p class="text-gray-300 mb-6">
                        A revolutionary networking platform that breaks down barriers and opens new possibilities for
                        global connection, knowledge sharing, and collaborative innovation.
                    </p>
                    <div class="flex justify-center">
                        <span class="bg-purple-600/20 text-purple-400 px-4 py-2 rounded-full text-sm font-medium">Coming Soon</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Works Section -->
    <section id="works" class="py-20 bg-gradient-to-br from-gray-900 to-xerv-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">Our Works</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Showcasing our team's innovative projects and creative solutions
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">

                <!-- Card 1: WADDLE App -->
                <a href="https://embed.figma.com/design/Lqwgk5O6IaImwfTsedGOAy/WADDLE-APP?node-id=2-101657&embed-host=share" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white text-3xl">
                                <i class="fas fa-figma"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">WADDLE App Prototype</h3>
                        <p class="text-gray-400">Interactive UI/UX prototype created in Figma with navigation flows and design system.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-red-900 bg-opacity-30 rounded-md text-red-400 text-xs">Figma</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Johnpaul</p>
                    </div>
                </a>

                <!-- Card 2: Paypi App -->
                <a href="https://www.figma.com/design/s7Zz9FMzAdoQi9OC6Uj5Md/Paypi-App?node-id=0-1&t=mh86NtCJmzuBSW2y-1" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-pink-500 to-red-600 text-white text-3xl">
                                <i class="fas fa-figma"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Paypi App Prototype</h3>
                        <p class="text-gray-400">High-fidelity payment app design with user flows, components, and interactive previews.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-red-900 bg-opacity-30 rounded-md text-red-400 text-xs">Figma</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Johnpaul</p>
                    </div>
                </a>

                <!-- Card 3: Milanochi Shopping Cart -->
                <a href="https://shopping-cart-milanochi.vercel.app" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-teal-600 text-white text-3xl">
                                <i class="fas fa-shopping-cart"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Milanochi Shopping Cart</h3>
                        <p class="text-gray-400">React-based e-commerce front end with product listing, cart functionality, and checkout flow.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
                            <span class="px-2 py-1 bg-teal-900 bg-opacity-30 rounded-md text-teal-400 text-xs">Tailwind CSS</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Daniel</p>
                    </div>
                </a>

                <!-- Card 4: Lendsqr FE Test -->
                <a href="https://ochi-daniel-lendsqr-fe-test.vercel.app" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-600 to-indigo-700 text-white text-3xl">
                                <i class="fas fa-laptop-code"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Lendsqr Frontend Test</h3>
                        <p class="text-gray-400">Assessment project covering user dashboard, data tables, and responsive layouts.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">Ant Design</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Daniel</p>
                    </div>
                </a>

                <!-- Card 5: Construction Landing Page -->
                <a href="https://construction-landing-page-blush.vercel.app/" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-green-500 to-yellow-600 text-white text-3xl">
                                <i class="fas fa-hard-hat"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Construction Landing Page</h3>
                        <p class="text-gray-400">SEO-optimized marketing site with service sections, image gallery, and contact form.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">HTML</span>
                            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">Tailwind CSS</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Abang</p>
                    </div>
                </a>

                <!-- Card 6: SoundMap Africa -->
                <a href="https://www.soundmap.africa" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white text-3xl">
                                <i class="fas fa-map-marked-alt"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">SoundMap Africa</h3>
                        <p class="text-gray-400">Music discovery platform integrating Mapbox for geotagged track exploration.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">MongoDB</span>
                            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">Express</span>
                            <span class="px-2 py-1 bg-cyan-900 bg-opacity-30 rounded-md text-cyan-400 text-xs">React</span>
                            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">Node.js</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Ifeoluwa</p>
                    </div>
                </a>

                <!-- Card 7: Flash Reviews -->
                <a href="https://www.flashreviews.ca" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-yellow-500 to-orange-600 text-white text-3xl">
                                <i class="fas fa-star"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Flash Reviews</h3>
                        <p class="text-gray-400">Quick feedback aggregator with rating system and user comments integration.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">MongoDB</span>
                            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">Express</span>
                            <span class="px-2 py-1 bg-cyan-900 bg-opacity-30 rounded-md text-cyan-400 text-xs">React</span>
                            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">Node.js</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Ifeoluwa</p>
                    </div>
                </a>

                <!-- Card 8: Quickero Platform -->
                <a href="https://quickeroplatform.vercel.app/" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 text-white text-3xl">
                                <i class="fas fa-rocket"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Quickero Platform</h3>
                        <p class="text-gray-400">Next.js app with task management features, real-time updates, and responsive design.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">Next.js</span>
                            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">Tailwind CSS</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Abang</p>
                    </div>
                </a>

                <!-- Card 9: JC Connects -->
                <a href="https://jcconnects.com" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-3xl">
                                <i class="fas fa-users"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">JC Connects</h3>
                        <p class="text-gray-400">JC CONNENT is a full-featured e-commerce platform offering cart, search, payments, user/product management, admin tools, messaging, and loyalty points.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
                            <span class="px-2 py-1 bg-yellow-900 bg-opacity-30 rounded-md text-yellow-400 text-xs">Laravel</span>
                            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">PHP</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Jonadab</p>
                    </div>
                </a>

                <!-- Card 10: AI Inspin Showcase -->
                <a href="https://joweb1.github.io/Ai-Inspin/" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 text-white text-3xl">
                                <i class="fas fa-brain"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">AI Inspin Showcase</h3>
                        <p class="text-gray-400">Static portfolio site highlighting AI-based design concepts and experiments.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">HTML</span>
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">CSS</span>
                            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">JavaScript</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Jonadab</p>
                    </div>
                </a>

                <!-- Card 11: Refit Framer Template -->
                <a href="https://refit.framer.website/?via=jjgerrish" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-green-500 to-teal-600 text-white text-3xl">
                                <i class="fas fa-home"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Refit Framer Template</h3>
                        <p class="text-gray-400">Customizable Framer template tailored for home improvement and service businesses.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-purple-900 bg-opacity-30 rounded-md text-purple-400 text-xs">Framer</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Osim</p>
                    </div>
                </a>

                <!-- Card 12: Sonic Framer Template -->
                <a href="https://sonic.framer.media/" target="_blank" rel="noopener noreferrer" class="block">
                    <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                        <div class="text-center mb-4">
                            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-3xl">
                                <i class="fas fa-music"></i>
                            </span>
                        </div>
                        <h3 class="font-heading text-xl font-bold text-white mb-2 text-center">Sonic Framer Template</h3>
                        <p class="text-gray-400">Dynamic Framer template for showcasing products, e-commerce, and portfolios.</p>
                        <div class="mt-4 flex justify-center space-x-2">
                            <span class="px-2 py-1 bg-purple-900 bg-opacity-30 rounded-md text-purple-400 text-xs">Framer</span>
                        </div>
                        <p class="text-gray-300 italic text-center mt-4">By Osim</p>
                    </div>
                </a>

            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gradient-to-br from-xerv-dark to-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">Get In Touch</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Ready to bring your vision to life? Let's collaborate!
                </p>
            </div>

            <!-- Main Contact Buttons -->
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16">
                <!-- Services Inquiry Button -->
                <div class="text-center">
                    <a href="mailto:<EMAIL>?subject=Service%20Inquiry%20-%20Project%20Collaboration&body=Hello%20Xerv%20Team%2C%0A%0AI%20am%20interested%20in%20your%20services.%20Please%20find%20my%20requirements%20below%3A%0A%0AService%20Type%3A%20%5BWeb%20Development%20%2F%20UI%20Design%20%2F%20Logo%20Design%20%2F%20Image%20Editing%5D%0AProject%20Description%3A%20%0ATimeline%3A%20%0ABudget%20Range%3A%20%0AContact%20Information%3A%20%0A%0ALooking%20forward%20to%20hearing%20from%20you.%0A%0ABest%20regards%2C%0A%5BYour%20Name%5D"
                       class="group block w-full">
                        <div class="glass-card contact-card-hover p-8 rounded-2xl hover:border-xerv-blue/50 transition-all duration-300">
                            <div class="w-16 h-16 bg-xerv-blue/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-xerv-blue/30 transition-colors">
                                <i class="fas fa-handshake text-2xl text-xerv-blue"></i>
                            </div>
                            <h3 class="font-heading text-2xl text-white mb-4">Get Our Services</h3>
                            <p class="text-gray-300 mb-6">
                                Ready to start your project? Get in touch for web development, UI design, logo creation, or image editing services.
                            </p>
                            <div class="bg-xerv-blue text-xerv-dark px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white transition-all duration-300 inline-block">
                                Start Your Project
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Careers Button -->
                <div class="text-center">
                    <a href="mailto:<EMAIL>?subject=Career%20Application%20-%20Join%20Xerv%20Team&body=Hello%20Xerv%20Team%2C%0A%0AI%20am%20interested%20in%20joining%20your%20team.%20Please%20find%20my%20details%20below%3A%0A%0APosition%20of%20Interest%3A%20%0AExperience%20Level%3A%20%0ASkills%3A%20%0AAvailability%3A%20%0A%0ANote%3A%20Resume%20%26%20Experience%20Not%20Required%20-%20We%20value%20passion%20and%20potential!%0A%0AThank%20you%20for%20considering%20my%20application.%0A%0ABest%20regards%2C%0A%5BYour%20Name%5D"
                       class="group block w-full">
                        <div class="glass-card contact-card-hover p-8 rounded-2xl hover:border-xerv-magenta/50 transition-all duration-300">
                            <div class="w-16 h-16 bg-xerv-magenta/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-xerv-magenta/30 transition-colors">
                                <i class="fas fa-users text-2xl text-xerv-magenta"></i>
                            </div>
                            <h3 class="font-heading text-2xl text-white mb-4">Join Our Team</h3>
                            <p class="text-gray-300 mb-6">
                                Passionate about technology? Join our mission to serve humanity through innovative digital solutions.
                            </p>
                            <div class="border-2 border-xerv-magenta text-xerv-magenta px-8 py-4 rounded-lg font-semibold text-lg hover:bg-xerv-magenta hover:text-white transition-all duration-300 inline-block">
                                Apply Now
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Urgent Hiring Section -->
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-8">
                    <h3 class="font-heading text-3xl text-white mb-4">🚨 Urgent Openings</h3>
                    <p class="text-gray-300">We're actively looking for passionate individuals to join our team!</p>
                </div>

                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Volunteer Social Media Manager -->
                    <div class="relative">
                        <div class="absolute -top-2 -right-2 z-10">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold urgent-badge">URGENT</span>
                        </div>
                        <a href="mailto:<EMAIL>?subject=Volunteer%20Application%20-%20Social%20Media%20Manager&body=Hello%20Xerv%20Team%2C%0A%0AI%20am%20interested%20in%20the%20Volunteer%20Social%20Media%20Manager%20position.%20Please%20find%20my%20details%20below%3A%0A%0AExperience%20with%20Social%20Media%3A%20%0AAvailable%20Hours%20per%20Week%3A%20%0ASkills%20%26%20Tools%3A%20%0APortfolio%2FExamples%3A%20%0A%0ANote%3A%20Resume%20%26%20Experience%20Not%20Required%20-%20We%20value%20passion%20and%20potential!%0A%0AThank%20you%20for%20considering%20my%20application.%0A%0ABest%20regards%2C%0A%5BYour%20Name%5D"
                           class="block">
                            <div class="glass-card p-6 rounded-xl hover:border-yellow-500/50 transition-all duration-300 hover:transform hover:scale-105">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-bullhorn text-xl text-yellow-500"></i>
                                    </div>
                                    <h4 class="font-heading text-xl text-white">Volunteer Social Media Manager</h4>
                                </div>
                                <p class="text-gray-300 text-sm">
                                    Help us grow our digital presence and engage with our community across social platforms.
                                </p>
                            </div>
                        </a>
                    </div>

                    <!-- Volunteer Web Developer -->
                    <div class="relative">
                        <div class="absolute -top-2 -right-2 z-10">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold urgent-badge">URGENT</span>
                        </div>
                        <a href="mailto:<EMAIL>?subject=Volunteer%20Application%20-%20Web%20Developer&body=Hello%20Xerv%20Team%2C%0A%0AI%20am%20interested%20in%20the%20Volunteer%20Web%20Developer%20position.%20Please%20find%20my%20details%20below%3A%0A%0AProgramming%20Languages%3A%20%0AFrameworks%20%26%20Technologies%3A%20%0AAvailable%20Hours%20per%20Week%3A%20%0APortfolio%2FGitHub%3A%20%0A%0ANote%3A%20Resume%20%26%20Experience%20Not%20Required%20-%20We%20value%20passion%20and%20potential!%0A%0AThank%20you%20for%20considering%20my%20application.%0A%0ABest%20regards%2C%0A%5BYour%20Name%5D"
                           class="block">
                            <div class="glass-card p-6 rounded-xl hover:border-green-500/50 transition-all duration-300 hover:transform hover:scale-105">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-code text-xl text-green-500"></i>
                                    </div>
                                    <h4 class="font-heading text-xl text-white">Volunteer Web Developer</h4>
                                </div>
                                <p class="text-gray-300 text-sm">
                                    Contribute to cutting-edge projects and expand your portfolio while making a real impact.
                                </p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Waitlist Section -->
    <section id="waitlist" class="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-xerv-dark">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="font-heading text-4xl md:text-6xl text-white mb-6">🚀 Join the Waitlist</h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
                    Be the first to experience TheMeet and NeTuArk. Early access, early power.
                </p>
            </div>

            <!-- Tally Embed -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
                <iframe
                    data-tally-src="https://tally.so/embed/wQWXep?alignLeft=1&hideTitle=1&transparentBackground=1&dynamicHeight=1"
                    loading="lazy"
                    width="100%"
                    height="400"
                    frameborder="0"
                    marginheight="0"
                    marginwidth="0"
                    title="🚀 Join the Waitlist – Be First to Experience TheMeet & NeTuark"
                    class="rounded-lg">
                </iframe>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black/50 border-t border-xerv-gray/30 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="font-heading text-3xl text-white mb-4">XERV</div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Serving humanity through innovative digital solutions. Building the future, one project at a time.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-xerv-blue transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-xerv-blue transition-colors">
                            <i class="fab fa-discord text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-xerv-blue transition-colors">
                            <i class="fab fa-telegram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-xerv-blue transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="font-heading text-lg text-white mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-400 hover:text-white transition-colors">Web Development</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-white transition-colors">UI Design</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-white transition-colors">Image Editing</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-white transition-colors">Logo Design</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-heading text-lg text-white mb-4">Projects</h3>
                    <ul class="space-y-2">
                        <li><a href="#projects" class="text-gray-400 hover:text-white transition-colors">TheMeet</a></li>
                        <li><a href="#projects" class="text-gray-400 hover:text-white transition-colors">NeTuArk</a></li>
                        <li><a href="#works" class="text-gray-400 hover:text-white transition-colors">Our Works</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#waitlist" class="text-gray-400 hover:text-white transition-colors">Join Waitlist</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-xerv-gray/30 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Xerv. All rights reserved. Serving Humanity Through Technology.</p>
            </div>
        </div>
    </footer>

    <!-- Tally Script -->
    <script>
        var d=document,w="https://tally.so/widgets/embed.js",v=function(){
            "undefined"!=typeof Tally?Tally.loadEmbeds():
            d.querySelectorAll("iframe[data-tally-src]:not([src])").forEach((function(e){
                e.src=e.dataset.tallySrc
            }))
        };
        if("undefined"!=typeof Tally) v();
        else if(d.querySelector('script[src="'+w+'"]')==null){
            var s=d.createElement("script");
            s.src=w; s.onload=v; s.onerror=v;
            d.body.appendChild(s);
        }
    </script>

    <!-- Enhanced JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // Close mobile menu if open
                    document.getElementById('mobile-menu').classList.add('hidden');
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections for animations
        document.querySelectorAll('section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });

        // Add active nav link highlighting
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('nav a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('text-xerv-blue');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('text-xerv-blue');
                }
            });
        });
    </script>
</body>
</html>

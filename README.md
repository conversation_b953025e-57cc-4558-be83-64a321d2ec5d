# Xerv - Serve Humanity Through Technology

A comprehensive website showcasing Xerv's mission, services, and innovative projects including TheMeet and NeTuArk.

## 🚀 Features

### Core Sections
- **Hero Section**: Powerful introduction with Xerv's mission "Serve Humanity"
- **About**: Our values and approach to technology
- **Services**: Web development, UI design, image editing, and logo design
- **Upcoming Projects**: TheMeet and NeTuArk with their respective mottos
- **Our Works**: Showcase of 12 completed projects by our team
- **Contact**: Comprehensive contact section with service inquiries and career applications
- **Waitlist**: Tally form integration for early access to upcoming projects

### Technical Features
- **Modern Design**: Built with Tailwind CSS and custom animations
- **Responsive**: Fully responsive design for all devices
- **Interactive**: Smooth scrolling, hover effects, and scroll-based animations
- **Performance**: Optimized loading and efficient animations
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support

## 🛠 Technology Stack

- **HTML5**: Semantic markup
- **Tailwind CSS**: Utility-first CSS framework
- **Custom CSS**: Enhanced animations and effects
- **JavaScript**: Interactive functionality and animations
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Bebas Neue, Inter, Poppins)
- **Tally**: Form integration for waitlist

## 📁 Project Structure

```
Xerv/
├── index.html              # Main website file
├── css/
│   └── style.css          # Custom styles and animations
├── js/
│   └── script.js          # Interactive functionality
├── admin.html             # Admin dashboard with client-side protection
├── logo2-removebg-preview.png  # NeTuArk logo
└── README.md              # This file
```

## 🎨 Design Elements

### Color Palette
- **Primary**: #0F1118 (Xerv Dark)
- **Accent Blue**: #00B7FF (Xerv Blue)
- **Accent Magenta**: #FF007F (Xerv Magenta)
- **Gray**: #262626 (Xerv Gray)
- **Light**: #F1F1F1 (Xerv Light)

### Typography
- **Headings**: Bebas Neue
- **Body**: Inter
- **Special**: Poppins

### Effects
- Glass morphism cards
- Particle background animation
- Smooth scroll animations
- Hover transformations
- Gradient overlays

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Xerv
   ```

2. **Start a local server**
   ```bash
   # Using Python
   python -m http.server 8000

   # Using Node.js
   npx serve .

   # Using PHP
   php -S localhost:8000
   ```

3. **Open in browser**
   Navigate to `http://localhost:8000`

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Project Showcase

### Featured Works (12 Projects)

1. **WADDLE App Prototype** - Figma UI/UX design by Johnpaul
2. **Paypi App Prototype** - Payment app design by Johnpaul
3. **Milanochi Shopping Cart** - React e-commerce by Daniel
4. **Lendsqr Frontend Test** - Dashboard application by Daniel
5. **Construction Landing Page** - Marketing site by Abang
6. **SoundMap Africa** - Music discovery platform by Ifeoluwa
7. **Flash Reviews** - Review aggregator by Ifeoluwa
8. **Quickero Platform** - Task management app by Abang
9. **JC Connects** - E-commerce platform by Jonadab
10. **AI Inspin Showcase** - AI design concepts by Jonadab
11. **Refit Framer Template** - Home improvement template by Osim
12. **Sonic Framer Template** - Product showcase template by Osim

### Upcoming Projects

- **TheMeet**: "Meet. Share. Collaborate" - Virtual meeting platform
- **NeTuArk**: "The World Is Opening" - Global networking platform

## 📞 Contact Features

### Service Inquiries
- **Email**: <EMAIL>
- **Pre-filled Templates**: Structured email templates for service requests
- **Services Available**: Web Development, UI Design, Logo Design, Image Editing

### Career Opportunities
- **General Applications**: Open positions for passionate individuals
- **Volunteer Positions**:
  - Social Media Manager (URGENT)
  - Web Developer (URGENT)
- **No Experience Required**: Focus on passion and potential

### Contact Methods
- **Mailto Links**: Direct email integration with pre-filled subjects and bodies
- **Responsive Design**: Optimized for all devices
- **Accessibility**: ARIA labels and keyboard navigation support

## 🔐 Admin Dashboard

### Access Methods
- **URL Hash**: `admin.html#xerv-admin-2024`
- **URL Parameter**: `admin.html?admin=xerv-2024`
- **Session Storage**: Maintains authentication during session

### Features
- **Site Statistics**: Visitor counts, contact inquiries, project views
- **Recent Activity**: Contact submissions and system status
- **Quick Actions**: Email management, analytics, settings
- **Security**: Client-side protection (demo purposes only)

### Default Behavior
- Shows 404 error page when accessed without proper credentials
- Redirects unauthorized users to error page
- Session-based authentication for convenience

## 🔧 Customization

### Adding New Projects
1. Add project card HTML in the "Our Works" section
2. Include appropriate technology tags
3. Add hover effects and animations

### Modifying Colors
Update the Tailwind config in `index.html`:
```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                'xerv-dark': '#0F1118',
                'xerv-blue': '#00B7FF',
                // Add your colors here
            }
        }
    }
}
```

### Custom Animations
Add new animations in `css/style.css`:
```css
@keyframes yourAnimation {
    from { /* start state */ }
    to { /* end state */ }
}
```

## 🌟 Performance Optimizations

- Lazy loading for images
- Debounced scroll handlers
- Efficient CSS animations
- Optimized asset loading
- Minimal JavaScript footprint

## 📞 Contact & Support

For questions about this website or Xerv's services:
- Visit our waitlist section to join early access
- Check out our portfolio in the "Our Works" section
- Explore our services for collaboration opportunities

## 📄 License

This project is part of Xerv's portfolio. All rights reserved.

---

**Xerv** - Serving Humanity Through Technology 🚀